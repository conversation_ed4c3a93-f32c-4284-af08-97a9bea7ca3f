/**
 * User Router for BuddyChip tRPC API
 * 
 * Handles user profile, subscription, and usage operations
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { protectedProcedure, publicProcedure, createTRPCRouter } from '../lib/trpc';
import { getOrCreateUser, getUserStats, canUserUseFeature, logUsage } from '../lib/user-service';
import { clerkClient } from '@clerk/nextjs/server';
import type { PlanFeature } from '../../prisma/generated';

export const userRouter = createTRPCRouter({
  /**
   * Get current user profile with plan and usage data
   */
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'You must be logged in to access this resource',
      });
    }

    try {
      // Get Clerk user data for fallback
      const clerk = await clerkClient();
      const clerkUser = await clerk.users.getUser(ctx.userId);
      
      // Get or create user in our database
      const user = await getOrCreateUser(ctx.userId, {
        email: clerkUser.emailAddresses[0]?.emailAddress,
        name: clerkUser.firstName && clerkUser.lastName 
          ? `${clerkUser.firstName} ${clerkUser.lastName}` 
          : clerkUser.firstName || undefined,
        avatar: clerkUser.imageUrl,
      });

      // Get user statistics
      const stats = await getUserStats(ctx.userId);

      return {
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          isAdmin: user.isAdmin,
          createdAt: user.createdAt,
          lastActiveAt: user.lastActiveAt,
        },
        plan: {
          id: user.plan.id,
          name: user.plan.name,
          displayName: user.plan.displayName,
          description: user.plan.description,
          price: user.plan.price,
          features: user.plan.features.map((f: PlanFeature) => ({
            feature: f.feature,
            limit: f.limit,
          })),
        },
        stats,
      };
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to get user profile',
      });
    }
  }),

  /**
   * Get user's current usage for all features
   */
  getUsage: publicProcedure.query(async ({ ctx }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: 'UNAUTHORIZED',
        message: 'You must be logged in to access this resource',
      });
    }

    try {
      const features = ['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH'];
      
      const usage = await Promise.all(
        features.map(async (feature) => {
          const result = await canUserUseFeature(ctx.userId!, feature);
          return {
            feature,
            ...result,
          };
        })
      );

      return usage;
    } catch (error) {
      console.error('Error getting user usage:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to get user usage',
      });
    }
  }),

  /**
   * Check if user can perform a specific action
   */
  canUseFeature: publicProcedure
    .input(z.object({
      feature: z.enum(['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB', 'TEAM_MEMBERS']),
    }))
    .query(async ({ ctx, input }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'You must be logged in to access this resource',
        });
      }

      try {
        return await canUserUseFeature(ctx.userId, input.feature);
      } catch (error) {
        console.error('Error checking feature usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to check feature availability',
        });
      }
    }),

  /**
   * Update user profile information
   */
  updateProfile: publicProcedure
    .input(z.object({
      name: z.string().optional(),
      // Note: email and avatar are managed by Clerk
    }))
    .mutation(async ({ ctx, input }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'You must be logged in to access this resource',
        });
      }

      try {
        // Update in Clerk if name is provided
        if (input.name) {
          const [firstName, ...lastNameParts] = input.name.split(' ');
          const clerk = await clerkClient();
          await clerk.users.updateUser(ctx.userId, {
            firstName,
            lastName: lastNameParts.join(' ') || undefined,
          });
        }

        // Update in our database
        const user = await ctx.prisma.user.update({
          where: { id: ctx.userId },
          data: {
            name: input.name,
            lastActiveAt: new Date(),
          },
        });

        return {
          success: true,
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            avatar: user.avatar,
          },
        };
      } catch (error) {
        console.error('Error updating user profile:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update profile',
        });
      }
    }),

  /**
   * Log feature usage (for tracking rate limits)
   */
  logUsage: publicProcedure
    .input(z.object({
      feature: z.enum(['AI_CALLS', 'IMAGE_GENERATIONS', 'MONITORED_ACCOUNTS', 'MENTIONS_PER_MONTH', 'STORAGE_GB', 'TEAM_MEMBERS']),
      amount: z.number().default(1),
      metadata: z.record(z.any()).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      if (!ctx.userId) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'You must be logged in to access this resource',
        });
      }

      try {
        // Check if user can use this feature
        const canUse = await canUserUseFeature(ctx.userId, input.feature);
        if (!canUse.allowed) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: `Usage limit exceeded for ${input.feature}. Current: ${canUse.currentUsage}, Limit: ${canUse.limit}`,
          });
        }

        // Log the usage
        await logUsage(ctx.userId, input.feature, input.amount, input.metadata);

        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error('Error logging usage:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to log usage',
        });
      }
    }),
});