"use client"
import { useState } from "react"
import {
  MessageSquare,
  Zap,
  Trash2,
  ChevronDown,
  ChevronUp,
  Lightbulb,
  LightbulbOff,
  Loader2,
  RefreshCw,
  RotateCcw,
  X,
} from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Textarea } from "@/components/ui/textarea"
import ShardLoadingAnimation from "@/components/ui/shard-loading-animation"
import ConfirmationDialog from "@/components/ui/confirmation-dialog"
import { trpc } from "@/utils/trpc"
import { toast } from "sonner"
import AuthenticatedNavbar from "@/components/authenticated-navbar"

// Type definitions
type ReplyGuyMention = {
  id: string
  content: string
  authorHandle: string
  authorName: string
  authorAvatar?: string
  tweetUrl: string
  platform: string
  createdAt: Date
  bullishScore?: number | null
  keywords?: string[]
  hasAIResponse: boolean
  account?: {
    id: string
    handle: string
    displayName?: string
    avatar?: string
    isActive: boolean
  } | null
  responses: Array<{
    id: string
    content: string
    model?: string | null
    confidence?: number | null
    tokensUsed?: number | null
    createdAt: Date
  }>
}

type MonitoredAccount = {
  id: string
  handle: string
  displayName?: string
  avatar?: string
  isActive: boolean
  platform: string
  followerCount: number
  createdAt: Date
  lastSyncAt?: Date | null
  mentionsCount: number
}

export default function ReplyGuyPage() {
  const [showMonitoredAccounts, setShowMonitoredAccounts] = useState(false)
  const [selectedResponse, setSelectedResponse] = useState<Record<string, string>>({})
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({})
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean
    responseId: string | null
    mentionId: string | null
  }>({
    isOpen: false,
    responseId: null,
    mentionId: null
  })

  // tRPC queries
  const { data: mentions, isLoading: mentionsLoading, refetch: refetchMentions } = trpc.mentions.getAll.useQuery({ limit: 20 })
  const { data: monitoredAccounts, isLoading: accountsLoading } = trpc.accounts.getMonitored.useQuery()

  // tRPC mutations
  const generateResponseMutation = trpc.benji.generateMentionResponse.useMutation()
  const enhanceMentionMutation = trpc.mentions.enhance.useMutation()
  const syncAllMutation = trpc.mentions.syncAllAccounts.useMutation()
  const syncAccountMutation = trpc.mentions.syncAccount.useMutation()
  const archiveMentionMutation = trpc.mentions.archive.useMutation()
  const deleteResponseMutation = trpc.mentions.deleteResponse.useMutation()

  const handleGenerateAIAnswer = async (mention: ReplyGuyMention) => {
    setLoadingStates(prev => ({ ...prev, [`generate-${mention.id}`]: true }))
    try {
      const response = await generateResponseMutation.mutateAsync({
        mentionId: mention.id,
        mentionContent: mention.content,
        authorInfo: {
          name: mention.authorName,
          handle: mention.authorHandle
        }
      })
      
      // Refetch mentions to get the new response
      await refetchMentions()
      toast.success("AI response generated successfully!")
    } catch (error) {
      console.error('Error generating AI response:', error)
      toast.error("Failed to generate AI response. Please try again.")
    } finally {
      setLoadingStates(prev => ({ ...prev, [`generate-${mention.id}`]: false }))
    }
  }

  const handleEnhanceMention = async (mentionId: string) => {
    try {
      await enhanceMentionMutation.mutateAsync({ mentionId })
      await refetchMentions()
      toast.success("Mention enhanced successfully!")
    } catch (error) {
      console.error('Error enhancing mention:', error)
      toast.error("Failed to enhance mention. Please try again.")
    }
  }

  const handleDeleteMention = async (mentionId: string) => {
    try {
      // Archive the mention instead of permanently deleting it
      await archiveMentionMutation.mutateAsync({ mentionId })
      await refetchMentions()
      toast.success("Mention archived successfully!")
    } catch (error) {
      console.error('Error archiving mention:', error)
      toast.error("Failed to archive mention. Please try again.")
    }
  }

  const handleResponseChange = (mentionId: string, responseContent: string) => {
    setSelectedResponse(prev => ({
      ...prev,
      [mentionId]: responseContent
    }))
  }

  const handleReplyToMention = (tweetUrl: string) => {
    // Open the tweet in a new tab for replying
    window.open(tweetUrl, '_blank', 'noopener,noreferrer')
  }

  const handleUseReply = async (mentionId: string, responseContent: string) => {
    // Open Twitter with pre-filled reply using URL parameters
    try {
      // Find the mention to get the tweet URL and extract tweet ID
      const mention = mentions?.mentions?.find((m: any) => m.id === mentionId)
      if (!mention) {
        toast.error("Could not find mention details.")
        return
      }

      // Extract tweet ID from the URL
      // Twitter URLs are like: https://twitter.com/username/status/1234567890 or https://x.com/username/status/1234567890
      const tweetId = mention.tweetUrl.match(/\/status\/(\d+)/)?.[1] || mention.id
      
      // Create Twitter compose URL with pre-filled reply
      // Format: https://twitter.com/intent/tweet?in_reply_to=TWEET_ID&text=RESPONSE_TEXT
      const encodedResponse = encodeURIComponent(responseContent)
      const replyUrl = `https://twitter.com/intent/tweet?in_reply_to=${tweetId}&text=${encodedResponse}`
      
      // Open Twitter with pre-filled reply
      window.open(replyUrl, '_blank', 'noopener,noreferrer')
      toast.success("Opening Twitter with pre-filled AI response!")
      
    } catch (error) {
      console.error('Failed to open Twitter with pre-filled reply:', error)
      // Fallback to copying to clipboard
      try {
        await navigator.clipboard.writeText(responseContent)
        const mention = mentions?.mentions?.find((m: any) => m.id === mentionId)
        if (mention) {
          window.open(mention.tweetUrl, '_blank', 'noopener,noreferrer')
        }
        toast.warning("Opened Twitter normally. AI response copied to clipboard as fallback.")
      } catch (clipboardError) {
        toast.error("Failed to open Twitter with pre-filled reply. Please copy manually.")
      }
    }
  }

  const handleRegenerateResponse = async (mention: ReplyGuyMention) => {
    setLoadingStates(prev => ({ ...prev, [`regenerate-${mention.id}`]: true }))
    try {
      await generateResponseMutation.mutateAsync({
        mentionId: mention.id,
        mentionContent: mention.content,
        authorInfo: {
          name: mention.authorName,
          handle: mention.authorHandle
        }
      })
      
      await refetchMentions()
      toast.success("Response regenerated successfully!")
    } catch (error) {
      console.error('Error regenerating response:', error)
      toast.error("Failed to regenerate response. Please try again.")
    } finally {
      setLoadingStates(prev => ({ ...prev, [`regenerate-${mention.id}`]: false }))
    }
  }

  const handleDeleteResponse = (responseId: string, mentionId: string) => {
    setDeleteDialog({
      isOpen: true,
      responseId,
      mentionId
    })
  }

  const confirmDeleteResponse = async () => {
    if (!deleteDialog.responseId || !deleteDialog.mentionId) return
    
    const { responseId, mentionId } = deleteDialog
    setLoadingStates(prev => ({ ...prev, [`delete-${responseId}`]: true }))
    
    try {
      await deleteResponseMutation.mutateAsync({ responseId })
      await refetchMentions()
      toast.success("AI response deleted successfully!")
    } catch (error) {
      console.error('Error deleting response:', error)
      toast.error("Failed to delete response. Please try again.")
    } finally {
      setLoadingStates(prev => ({ ...prev, [`delete-${responseId}`]: false }))
    }
  }

  const handleSyncAllMentions = async () => {
    try {
      const result = await syncAllMutation.mutateAsync()
      
      // Refetch mentions to show new data
      await refetchMentions()
      
      if (result.success) {
        let message = result.message || `Synced ${result.totalNewMentions} new mentions!`
        
        // Check if any accounts hit limits
        const limitReachedAccounts = result.accountResults?.filter((r: any) => r.limitReached) || []
        if (limitReachedAccounts.length > 0) {
          message += ` (${limitReachedAccounts.length} account(s) reached limit)`
        }
        
        toast.success(message)
      } else {
        toast.warning(`Sync completed with ${result.errors.length} errors`)
      }
    } catch (error) {
      console.error('Error syncing all mentions:', error)
      toast.error("Failed to sync mentions. Please try again.")
    }
  }

  const handleSyncAccount = async (accountId: string) => {
    try {
      const result = await syncAccountMutation.mutateAsync({ accountId })
      
      // Refetch mentions to show new data
      await refetchMentions()
      
      toast.success(result.message || "Account synced successfully!")
    } catch (error) {
      console.error('Error syncing account:', error)
      toast.error("Failed to sync account. Please try again.")
    }
  }

  return (
    <div className="min-h-screen p-4 md:p-8 font-sans bg-app-background text-app-headline">
      <header className="text-center mb-8">
        <h1 className="text-4xl md:text-5xl font-bold tracking-wider text-app-headline">REPLY GUY</h1>
      </header>
      <AuthenticatedNavbar currentPage="reply-guy" />

      <section className="mb-8">
        <div className="flex flex-wrap gap-4 items-center">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="border-app-stroke bg-app-card hover:bg-app-main hover:text-app-secondary text-app-headline"
                onClick={() => setShowMonitoredAccounts(!showMonitoredAccounts)}
              >
                Monitored Accounts
                {showMonitoredAccounts ? (
                  <ChevronUp className="ml-2 h-4 w-4" />
                ) : (
                  <ChevronDown className="ml-2 h-4 w-4" />
                )}
              </Button>
            </DropdownMenuTrigger>
          <DropdownMenuContent className="w-64 bg-app-card border-app-stroke text-app-headline shadow-lg">
            <DropdownMenuLabel className="text-app-headline opacity-75">Currently Monitored</DropdownMenuLabel>
            <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
            {accountsLoading ? (
              <DropdownMenuItem disabled className="flex justify-center items-center py-4">
                <ShardLoadingAnimation size={48} />
              </DropdownMenuItem>
            ) : monitoredAccounts?.accounts?.length === 0 ? (
              <DropdownMenuItem disabled className="text-center opacity-60">
                No monitored accounts
              </DropdownMenuItem>
            ) : (
              monitoredAccounts?.accounts?.map((account: any) => (
                <DropdownMenuItem
                  key={account.id}
                  className="flex justify-between items-center hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary"
                >
                  <span>@{account.handle}</span>
                  {account.isActive ? (
                    <Lightbulb className="w-4 h-4 text-app-main" />
                  ) : (
                    <LightbulbOff className="w-4 h-4 text-app-highlight" />
                  )}
                </DropdownMenuItem>
              ))
            )}
            <DropdownMenuSeparator className="bg-app-stroke opacity-50" />
            <DropdownMenuItem className="hover:bg-app-main hover:text-app-secondary focus:bg-app-main focus:text-app-secondary">
              Manage Accounts...
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button
          onClick={handleSyncAllMentions}
          disabled={syncAllMutation.isPending || accountsLoading}
          className="bg-app-main text-app-secondary hover:bg-app-highlight disabled:opacity-50"
        >
          {syncAllMutation.isPending ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin mr-2" />
              Syncing...
            </>
          ) : (
            <>
              <RefreshCw className="w-4 h-4 mr-2" />
              Sync Mentions
            </>
          )}
        </Button>
        </div>
      </section>

      <main className="space-y-6">
        {mentionsLoading ? (
          <div className="flex items-center justify-center py-8">
            <ShardLoadingAnimation size={64} />
          </div>
        ) : mentions?.mentions?.length === 0 ? (
          <div className="text-center py-10 text-app-headline opacity-60">
            <p className="text-lg">No mentions to display.</p>
            <p>Check back later or adjust your monitored accounts.</p>
          </div>
        ) : (
          mentions?.mentions?.map((mention: any) => (
          <Card key={mention.id} className="bg-app-card border-app-stroke text-app-headline rounded-lg shadow-md">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg text-app-headline">@{mention.authorHandle}</CardTitle>
                  <p className="text-xs text-app-headline opacity-70">
                    {new Date(mention.createdAt).toLocaleDateString()} via {mention.platform}
                  </p>
                  <p className="text-sm text-app-headline opacity-80">{mention.authorName}</p>
                </div>
                <div className="flex space-x-1 md:space-x-2">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="text-app-main hover:text-app-highlight px-2"
                    onClick={() => handleReplyToMention(mention.tweetUrl)}
                  >
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Reply
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`px-2 transition-colors ${
                      mention.responses?.length > 0
                        ? "text-app-highlight hover:text-app-main"
                        : "text-app-headline opacity-40 cursor-not-allowed"
                    }`}
                    onClick={() => mention.responses?.length > 0 && handleEnhanceMention(mention.id)}
                    disabled={enhanceMentionMutation.isPending || mention.responses?.length === 0}
                  >
                    {enhanceMentionMutation.isPending ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-1" />
                        Enhancing...
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-1" /> Enhance
                      </>
                    )}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-app-highlight hover:text-app-main px-2"
                    onClick={() => handleDeleteMention(mention.id)}
                    disabled={archiveMentionMutation.isPending}
                  >
                    {archiveMentionMutation.isPending ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-1" />
                    ) : (
                      <Trash2 className="w-4 h-4 mr-1" />
                    )}
                    Delete
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-app-headline opacity-90 leading-relaxed mb-4">{mention.content}</p>
              {mention.responses && mention.responses.length > 0 && (
                <div className="mt-4 space-y-3">
                  {mention.responses.map((response: any, index: number) => (
                    <div key={response.id} className="p-3 bg-app-background rounded-md border border-app-stroke">
                      <div className="flex items-center justify-between mb-2">
                        <p className="text-sm font-semibold text-app-main">
                          AI Generated Response {mention.responses.length > 1 ? `(${index + 1})` : ''}
                        </p>
                        {response.confidence && (
                          <span className="text-xs text-app-headline opacity-70">
                            Confidence: {Math.round(response.confidence * 100)}%
                          </span>
                        )}
                      </div>
                      <Textarea
                        value={selectedResponse[mention.id] || response.content}
                        onChange={(e) => handleResponseChange(mention.id, e.target.value)}
                        className="bg-app-card border-app-stroke text-app-headline text-sm min-h-[80px]"
                        rows={3}
                      />
                      <div className="flex justify-between items-center mt-2">
                        <div className="text-xs text-app-headline opacity-60">
                          {response.model && `Model: ${response.model}`}
                          {response.used && (
                            <span className="ml-2 text-app-main">• Used</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteResponse(response.id, mention.id)}
                            disabled={loadingStates[`delete-${response.id}`]}
                            className="text-rose-400 border-rose-200 hover:text-rose-500 hover:bg-rose-50 hover:border-rose-300 px-3 py-1"
                          >
                            {loadingStates[`delete-${response.id}`] ? (
                              <Loader2 className="w-4 h-4 animate-spin mr-1" />
                            ) : (
                              <X className="w-4 h-4 mr-1" />
                            )}
                            Delete
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRegenerateResponse(mention)}
                            disabled={loadingStates[`regenerate-${mention.id}`]}
                            className="border-app-stroke text-app-headline hover:bg-app-main hover:text-app-secondary"
                          >
                            {loadingStates[`regenerate-${mention.id}`] ? (
                              <Loader2 className="w-4 h-4 animate-spin mr-1" />
                            ) : (
                              <RotateCcw className="w-4 h-4 mr-1" />
                            )}
                            Regenerate
                          </Button>
                          <Button 
                            size="sm" 
                            onClick={() => handleUseReply(mention.id, selectedResponse[mention.id] || response.content)}
                            className="bg-app-main text-app-secondary hover:bg-app-highlight"
                          >
                            Use this reply
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex justify-end pt-3">
              <Button
                onClick={() => handleGenerateAIAnswer(mention)}
                disabled={loadingStates[`generate-${mention.id}`]}
                className="bg-app-main text-app-secondary hover:bg-app-highlight"
              >
                {loadingStates[`generate-${mention.id}`] ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Zap className="w-4 h-4 mr-2" /> Generate AI Answer
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
          ))
        )}
      </main>

      {/* Delete Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={deleteDialog.isOpen}
        onClose={() => setDeleteDialog({ isOpen: false, responseId: null, mentionId: null })}
        onConfirm={confirmDeleteResponse}
        title="Delete AI Response"
        message="Are you sure you want to delete this AI response? Think ultra carefully about this decision. This action cannot be undone and the response will be permanently removed."
        confirmText="Yes, Delete"
        cancelText="Cancel"
        confirmVariant="default"
      />
    </div>
  )
}