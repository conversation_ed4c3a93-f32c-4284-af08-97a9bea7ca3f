import { QueryCache, QueryClient } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { toast } from 'sonner';
import type { AppRouter } from '@/types/api';

export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: (error) => {
      toast.error(error.message, {
        action: {
          label: "retry",
          onClick: () => {
            queryClient.invalidateQueries();
          },
        },
      });
    },
  }),
});

export const trpc = createTRPCReact<AppRouter>();

export const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: '/api/trpc',
      fetch(url, options) {
        console.log('🔄 tRPC Client: Making request to:', url);
        console.log('📝 tRPC Client: Request options:', options);
        return fetch(url, {
          ...options,
          headers: {
            ...options?.headers,
            'Content-Type': 'application/json',
          },
        }).then(response => {
          console.log('📄 tRPC Client: Response status:', response.status);
          if (!response.ok) {
            console.error('❌ tRPC Client: Response not ok:', response.status, response.statusText);
          }
          return response;
        }).catch(error => {
          console.error('❌ tRPC Client: Fetch error:', error);
          throw error;
        });
      },
    }),
  ],
});

